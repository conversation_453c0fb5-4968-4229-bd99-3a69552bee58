<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Generation Interface</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="/static/style.css">
</head>

<body>
    <div class="container">
        <h1 class="mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" fill="currentColor" class="bi bi-images me-2"
                viewBox="0 0 16 16">
                <path d="M4.502 9a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3" />
                <path
                    d="M14.002 13a2 2 0 0 1-2 2h-10a2 2 0 0 1-2-2V5A2 2 0 0 1 2 3a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v8a2 2 0 0 1-1.998 2M14 2H4a1 1 0 0 0-1 1h9.002a2 2 0 0 1 2 2v7A1 1 0 0 0 15 11V3a1 1 0 0 0-1-1M2.002 4a1 1 0 0 0-1 1v8l2.646-2.354a.5.5 0 0 1 .63-.062l2.66 1.773 3.71-3.71a.5.5 0 0 1 .577-.094l1.777 1.947V5a1 1 0 0 0-1-1h-10" />
            </svg>
            Batch Image Generator
        </h1>

        <div class="row mb-4">
            <div class="col-md-10 mx-auto">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">⚠️ Required Setup</h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>
                                <strong>Install Google Cloud SDK:</strong>
                                <a href="https://dl.google.com/dl/cloudsdk/channels/rapid/GoogleCloudSDKInstaller.exe"
                                    class="btn btn-sm btn-outline-primary ms-2" target="_blank">
                                    Download for Windows
                                </a>
                                <a href="https://cloud.google.com/sdk/docs/install"
                                    class="btn btn-sm btn-outline-secondary ms-2" target="_blank">
                                    Other Platforms
                                </a>
                            </li>
                            <li class="mt-2">
                                <strong>Run these commands in Command Prompt:</strong>
                                <div class="bg-dark text-light p-2 mt-1 rounded">
                                    <code>gcloud auth application-default login</code>
                                </div>
                                <div class="bg-dark text-light p-2 mt-1 rounded">
                                    <code>gcloud auth application-default set-quota-project [YOUR_PROJECT_ID]</code>
                                </div>
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-10 mx-auto">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Batch Processing Settings</h5>
                    </div>
                    <div class="card-body">
                        <form id="generationForm">
                            <div class="mb-3" id="promptsTextareaContainer">
                                <label for="prompts" class="form-label">Prompts (one per line)</label>
                                <textarea class="form-control" id="prompts" name="prompts"></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="promptFiles" class="form-label">Or upload prompt files</label>
                                <input type="file" class="form-control" id="promptFiles" accept=".txt" multiple>
                                <div class="form-text">You can select multiple .txt files with prompts</div>
                            </div>

                            <div id="selectedFilesContainer" class="mb-3" style="display: none;">
                                <label class="form-label">Selected Prompt Files:</label>
                                <div id="selectedFilesList" class="list-group">
                                    <!-- Files will be listed here -->
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-danger mt-2" id="clearFilesBtn">
                                    Clear Files
                                </button>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="sampleCount" class="form-label">Images per prompt (1-8)</label>
                                    <input type="number" class="form-control" id="sampleCount" name="sampleCount"
                                        min="1" max="8" value="4">
                                </div>
                                <div class="col-md-6">
                                    <label for="aspectRatio" class="form-label">Aspect Ratio</label>
                                    <select class="form-select" id="aspectRatio" name="aspectRatio">
                                        <option value="16:9" selected>Landscape (16:9)</option>
                                        <option value="9:16">Portrait (9:16)</option>
                                        <option value="1:1">Square (1:1)</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="personGeneration" class="form-label">Person Generation</label>
                                    <select class="form-select" id="personGeneration" name="personGeneration">
                                        <option value="allow_all" selected>Allow All</option>
                                        <option value="allow_adult">Allow Adult Only</option>
                                        <option value="block_all">Block All</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="safetyFilter" class="form-label">Safety Filter</label>
                                    <select class="form-select" id="safetyFilter" name="safetyFilter">
                                        <option value="block_few" selected>Block Few</option>
                                        <option value="block_some">Block Some</option>
                                        <option value="block_most">Block Most</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="seedOption" class="form-label">Seed Option</label>
                                    <select class="form-select" id="seedOption" name="seedOption">
                                        <option value="1" selected>Random seed for each prompt</option>
                                        <option value="2">Same seed for all prompts</option>
                                        <option value="3">Incremental seeds</option>
                                    </select>
                                </div>
                                <div class="col-md-6" id="baseSeedContainer" style="display: none;">
                                    <label for="baseSeed" class="form-label">Base Seed</label>
                                    <input type="number" class="form-control" id="baseSeed" name="baseSeed" min="1">
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="addWatermark"
                                            name="addWatermark">
                                        <label class="form-check-label" for="addWatermark">
                                            Add Watermark
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="maxWorkers" class="form-label">Concurrent Workers (1-10)</label>
                                    <input type="number" class="form-control" id="maxWorkers" name="maxWorkers" min="1"
                                        max="10" value="5">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="projectId" class="form-label">Google Cloud Project ID</label>
                                <input type="text" class="form-control" id="projectId" name="projectId"
                                    placeholder="e.g., qwiklabs-gcp-01-492303c44319">
                            </div>

                            <button type="submit" class="btn btn-primary btn-lg w-100" id="generateBtn">Start
                                Generation</button>

                            <!-- Hidden status panel for tracking purposes only -->
                            <div id="statusPanel" style="display: none;"></div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const form = document.getElementById('generationForm');
            const generateBtn = document.getElementById('generateBtn');
            const statusPanel = document.getElementById('statusPanel');
            const seedOption = document.getElementById('seedOption');
            const baseSeedContainer = document.getElementById('baseSeedContainer');
            const promptTextarea = document.getElementById('prompts');
            const promptFileInput = document.getElementById('promptFile');

            let progressInterval;
            let isGenerating = false;

            // Show/hide base seed input based on seed option
            seedOption.addEventListener('change', function () {
                if (this.value === '2' || this.value === '3') {
                    baseSeedContainer.style.display = 'block';
                } else {
                    baseSeedContainer.style.display = 'none';
                }
            });

            // Variables to store selected files and their contents
            let selectedFiles = [];
            let combinedPrompts = "";

            const promptsTextareaContainer = document.getElementById('promptsTextareaContainer');
            const selectedFilesContainer = document.getElementById('selectedFilesContainer');
            const selectedFilesList = document.getElementById('selectedFilesList');
            const clearFilesBtn = document.getElementById('clearFilesBtn');
            const promptFilesInput = document.getElementById('promptFiles');

            // Handle file input change
            promptFilesInput.addEventListener('change', function (e) {
                const files = e.target.files;
                if (!files || files.length === 0) return;

                // Clear previous files if any
                selectedFiles = [];
                combinedPrompts = "";
                selectedFilesList.innerHTML = "";

                // Process each selected file
                let filesProcessed = 0;
                let totalFiles = files.length;

                Array.from(files).forEach(file => {
                    const reader = new FileReader();

                    reader.onload = function (e) {
                        const content = e.target.result;
                        const promptCount = content.split('\n')
                            .filter(line => line.trim()).length;

                        // Store file info
                        selectedFiles.push({
                            name: file.name,
                            content: content,
                            promptCount: promptCount
                        });

                        // Add to combined prompts
                        combinedPrompts += content + "\n";

                        // Create list item for this file
                        const listItem = document.createElement('div');
                        listItem.className = 'list-group-item d-flex justify-content-between align-items-center';
                        listItem.innerHTML = `
                            <span>${file.name}</span>
                            <span class="badge bg-primary rounded-pill">${promptCount} prompts</span>
                        `;
                        selectedFilesList.appendChild(listItem);

                        // Check if all files are processed
                        filesProcessed++;
                        if (filesProcessed === totalFiles) {
                            // Show the files list and hide textarea
                            selectedFilesContainer.style.display = 'block';
                            promptsTextareaContainer.style.display = 'none';

                            // Set the combined content to the hidden textarea
                            promptTextarea.value = combinedPrompts;
                        }
                    };

                    reader.readAsText(file);
                });
            });

            // Clear files button
            clearFilesBtn.addEventListener('click', function () {
                selectedFiles = [];
                combinedPrompts = "";
                promptTextarea.value = "";
                selectedFilesList.innerHTML = "";
                promptFilesInput.value = "";

                // Hide files list and show textarea
                selectedFilesContainer.style.display = 'none';
                promptsTextareaContainer.style.display = 'block';
            });

            // Handle form submission
            form.addEventListener('submit', function (e) {
                e.preventDefault();

                if (isGenerating) {
                    alert('Generation is already in progress. Please wait for it to complete.');
                    return;
                }

                const prompts = promptTextarea.value.trim();
                if (!prompts) {
                    alert('Please enter at least one prompt or upload a text file');
                    return;
                }

                const promptLines = prompts.split('\n').filter(line => line.trim());
                if (promptLines.length === 0) {
                    alert('Please enter at least one valid prompt');
                    return;
                }

                // Validate seed option
                if ((seedOption.value === '2' || seedOption.value === '3') &&
                    !document.getElementById('baseSeed').value) {
                    alert('Please enter a base seed value');
                    return;
                }

                // Prepare form data
                const formData = {
                    prompts: prompts,
                    sample_count: parseInt(document.getElementById('sampleCount').value),
                    aspect_ratio: document.getElementById('aspectRatio').value,
                    person_generation: document.getElementById('personGeneration').value,
                    safety_filter_level: document.getElementById('safetyFilter').value,
                    add_watermark: document.getElementById('addWatermark').checked,
                    seed_option: seedOption.value,
                    base_seed: document.getElementById('baseSeed').value || null,
                    max_workers: parseInt(document.getElementById('maxWorkers').value),
                    project_id: document.getElementById('projectId').value || null
                };

                // Disable form and update UI
                generateBtn.disabled = true;
                generateBtn.textContent = 'Generating Images...';
                isGenerating = true;

                // Show processing notification
                const notification = document.createElement('div');
                notification.className = 'alert alert-info mt-3';
                notification.innerHTML = `
                    <h4 class="alert-heading">Processing Started!</h4>
                    <p><strong>Please check the opened CMD/BAT file window for real-time progress updates.</strong></p>
                    <hr>
                    <p class="mb-0">Generated images will be saved to the <code>generated_images</code> folder.</p>
                `;
                form.appendChild(notification);

                // Get the base URL for API calls (handles both http and file protocols)
                const baseUrl = window.location.protocol === 'file:'
                    ? 'http://localhost:5000'
                    : '';

                // Start progress polling
                startProgressPolling();

                // Send request
                fetch(`${baseUrl}/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.error) {
                            showErrorNotification(data.error);
                            resetUI();
                        } else {
                            console.log(`Processing ${data.prompt_count} prompts...`);
                        }
                    })
                    .catch(error => {
                        console.error('Fetch error:', error);
                        showErrorNotification('Connection error. Make sure the Flask server is running at http://localhost:5000');
                        resetUI();
                    });
            });

            function resetUI() {
                generateBtn.disabled = false;
                generateBtn.textContent = 'Start Generation';
                isGenerating = false;
                stopProgressPolling();

                // Remove any existing notification
                const existingNotification = form.querySelector('.alert');
                if (existingNotification) {
                    form.removeChild(existingNotification);
                }

                // Add notification about checking the CMD window
                const statusNotification = document.createElement('div');
                statusNotification.className = 'alert alert-info mt-3';
                statusNotification.innerHTML = `
                    <p><strong>Please check the opened CMD/BAT file window for progress updates.</strong></p>
                    <hr>
                    <p class="mb-0">Generated images will be saved to the <code>generated_images</code> folder.</p>
                `;
                form.appendChild(statusNotification);

                // Remove the notification after 10 seconds
                setTimeout(() => {
                    if (statusNotification.parentNode) {
                        statusNotification.parentNode.removeChild(statusNotification);
                    }
                }, 10000);
            }

            function startProgressPolling() {
                // Initial poll
                pollProgress();

                // Set interval for polling
                progressInterval = setInterval(pollProgress, 2000);
            }

            function stopProgressPolling() {
                clearInterval(progressInterval);
            }

            function showErrorNotification(errorMessage) {
                // Remove any existing notification
                const existingNotification = form.querySelector('.alert');
                if (existingNotification) {
                    form.removeChild(existingNotification);
                }

                // Add error notification
                const errorNotification = document.createElement('div');
                errorNotification.className = 'alert alert-danger mt-3';
                errorNotification.innerHTML = `
                    <h4 class="alert-heading">Error!</h4>
                    <p>${errorMessage}</p>
                `;
                form.appendChild(errorNotification);

                // Remove the error notification after 10 seconds
                setTimeout(() => {
                    if (errorNotification.parentNode) {
                        errorNotification.parentNode.removeChild(errorNotification);
                    }
                }, 10000);
            }

            function pollProgress() {
                // Get the base URL for API calls (handles both http and file protocols)
                const baseUrl = window.location.protocol === 'file:'
                    ? 'http://localhost:5000'
                    : '';

                fetch(`${baseUrl}/progress`)
                    .then(response => response.json())
                    .then(data => {
                        // If processing is complete, stop polling and enable form
                        if (data.status === 'completed' || data.status.startsWith('error') || !data.is_processing) {
                            if (data.status === 'completed') {
                                // Success - handled by resetUI
                            } else if (data.status.startsWith('error')) {
                                showErrorNotification('Error: ' + data.status.substring(7));
                            }
                            resetUI();
                        }
                    })
                    .catch(error => {
                        console.error('Error polling progress:', error);
                    });
            }

            // Display a warning if opened directly from file system
            if (window.location.protocol === 'file:') {
                alert('You are opening this page directly from the file system.\n\n' +
                    'For the application to work correctly:\n' +
                    '1. Make sure the Flask server is running (run_server.bat)\n' +
                    '2. Access the page through http://localhost:5000\n\n' +
                    'The page will try to connect to localhost:5000 for API calls.');
            }
        });
    </script>

    <footer class="mt-5 text-center pb-3">
        <div class="container">
            <hr>
            <small class="text-muted">
                <a href="https://whatsapp.com/channel/0029VaUHU8zFHWpwV8Qu602l" target="_blank"
                    class="text-decoration-none">
                    Support me
                </a>
            </small>
        </div>
    </footer>
</body>

</html>