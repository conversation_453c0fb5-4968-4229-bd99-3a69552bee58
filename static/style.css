/* Custom styles for Image Generation Interface */

body {
    padding: 20px;
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.container {
    max-width: 1200px;
}

h1 {
    color: #0d6efd;
    margin-bottom: 1.5rem;
    font-weight: 600;
    text-align: center;
}

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
    padding: 12px 20px;
}

.card-body {
    padding: 20px;
}

.progress {
    height: 25px;
    border-radius: 8px;
    margin-top: 10px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    transition: width 0.5s ease;
    font-weight: 600;
}

textarea {
    min-height: 200px;
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 12px;
    font-family: 'Courier New', Courier, monospace;
    resize: vertical;
}

.form-control:focus,
.form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 8px;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
    transition: background-color 0.3s ease;
    padding: 10px 20px;
    font-weight: 600;
    border-radius: 6px;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

.btn-primary:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
}

.status-box {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    text-align: center;
    transition: all 0.5s ease;
}

.status-idle {
    background-color: #e2e3e5;
}

.status-processing {
    background-color: #cff4fc;
    animation: pulse 2s infinite;
}

.status-completed {
    background-color: #d1e7dd;
}

.status-error {
    background-color: #f8d7da;
}

/* Custom animation for status changes */
@keyframes pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }

    100% {
        opacity: 1;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .card {
        margin-bottom: 15px;
    }

    h1 {
        font-size: 1.8rem;
    }
}

/* Counter cards */
.card.bg-light {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.card.bg-light:hover {
    background-color: #e9ecef;
}

.card.bg-light h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #0d6efd;
    margin-bottom: 5px;
}

.card.bg-light p {
    color: #6c757d;
    font-weight: 500;
}

/* Input group styling */
.input-group {
    margin-bottom: 10px;
}

.input-group-text {
    background-color: #e9ecef;
    font-weight: 500;
}

/* Form elements */
.form-check-label {
    font-weight: normal;
}

.form-text {
    color: #6c757d;
    margin-top: 5px;
}

/* File input styling */
input[type="file"] {
    padding: 8px;
}

/* Current prompt textarea */
#currentPrompt {
    min-height: 80px;
    background-color: #f8f9fa;
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.9rem;
}